<script lang="ts">
	import Header from '$lib/components/Header.svelte';
	import TimeTracker from '$lib/components/TimeTracker.svelte';

	import { makeClient } from '$lib/make-client.js';
	import { onMount, onDestroy } from 'svelte';

	const client = makeClient(fetch);

	let fetchPromise = $state<Promise<any>>(Promise.resolve({}));
	let currentController: AbortController | null = null;
	let timeEntries = $state<any>({});

	async function getTimeEntries() {
		// Cancel previous request if it's still pending
		if (currentController) {
			currentController.abort();
		}

		// Create new abort controller for this request
		currentController = new AbortController();

		// Set the promise immediately so the UI shows loading state
		fetchPromise = (async () => {
			try {
				const timeEntriesData = await client.tracker.$get(
					{},
					{
						init: {
							signal: currentController.signal
						}
					}
				);

				if (!timeEntriesData.ok) {
					throw new Error('Failed to fetch time entries');
				}

				timeEntries = await timeEntriesData.json();

				return timeEntries;
			} catch (error) {
				// Don't throw if the request was aborted
				if (error instanceof Error && error.name === 'AbortError') {
					console.log('Request was cancelled');
					return null; // Return null for cancelled requests
				}
				throw error;
			}
		})();

		return fetchPromise;
	}

	function cleanup() {
		// Cancel any pending requests
		if (currentController) {
			currentController.abort();
		}
	}

	onMount(async () => {
		await getTimeEntries();
	});

	onDestroy(() => {
		cleanup();
		console.log('Tracker destroyed');
	});
</script>

<div class="container">

	<TimeTracker onTimerStopped={getTimeEntries} />

	<!-- <button class="bg-green-200 p-4" onclick={createTimeEntry}>createTimeEntry</button>-->
	<button class="bg-blue-200 p-4" onclick={getTimeEntries}>Refresh Time Entries</button>

	{#await fetchPromise}
		<p>Loading...</p>
	{:then data}
		{#if data !== null}
			{#each timeEntries.data as entry}
				<div>
					{new Date(entry.start_time).toLocaleDateString(undefined, {
						year: 'numeric',
						month: 'long',
						day: 'numeric',
						hour: 'numeric',
						minute: 'numeric',
						second: 'numeric'
					})}
				</div>
			{/each}
			<pre class="opacity-50">{JSON.stringify(timeEntries, null, 2)}</pre>
		{:else}
			<p>Request was cancelled</p>
		{/if}
	{:catch error}
		<p>Error: {error.message}</p>
	{/await}
</div>

<pre>
    <!-- {JSON.stringify(data, null, 2)} -->
</pre>
