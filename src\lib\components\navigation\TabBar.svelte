<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import { page } from '$app/state';

	import TimeTracker from '$lib/components/TimeTracker.svelte';

	// Icons
	import SolarClockSquareBoldDuotone from '~icons/solar/clock-square-bold-duotone';
	import SolarRoundGraphLineDuotone from '~icons/solar/round-graph-line-duotone';
	import SolarCaseRoundBoldDuotone from '~icons/solar/case-round-bold-duotone';
	import SolarUserCircleBoldDuotone from '~icons/solar/user-circle-bold-duotone';

	const navItems = [
		{
			label: 'Tracker',
			icon: SolarClockSquareBoldDuotone,
			link: '/tracker'
		},
		{
			label: 'Reports',
			icon: SolarRoundGraphLineDuotone,
			link: '/reports'
		},
		{
			label: 'Manager',
			icon: SolarCaseRoundBoldDuotone,
			link: '/manager'
		},
		{
			label: 'Account',
			icon: SolarUserCircleBoldDuotone,
			link: '/account'
		}
	];

	let navEl: HTMLDivElement | null = $state(null);
</script>

<div class="pointer-events-none w-full" style="--nav-height: {navEl?.clientHeight}px">
	<div
		bind:this={navEl}
		class="shadow-card-shadow pointer-events-auto fixed right-0 bottom-4 left-0 z-100 mx-auto w-full max-w-[22.5rem] rounded-2xl bg-(image:--color-card-gradient) px-2 shadow-[0_8px_16px_8px] backdrop-blur-xs"
	>
		<TimeTracker />

		<div class="flex h-full w-full items-center justify-between gap-2">
			{#each navItems as item}
				<a
					href={item.link}
					class={twMerge(
						'text-text-primary justify-top flex w-full flex-col items-center gap-1 py-2 opacity-70 transition-opacity hover:opacity-100',
						item.link === page.url.pathname
							? 'text-primary bg-bg border-border rounded-2xl border opacity-100'
							: ''
					)}
				>
					<item.icon class="size-6" />
					<span class="text-xs font-medium">{item.label}</span>
				</a>
			{/each}
		</div>
	</div>
</div>

{@html `<style>body { padding-bottom: ${navEl?.clientHeight}px; }</style>`}
